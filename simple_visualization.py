#!/usr/bin/env python3
"""
简单的道路结冰预测可视化脚本
从事件中随机选择4个片段，绘制预测概率曲线，将实际凝冰背景设为蓝色
"""
import os
import sys
import json
import argparse
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
import torch
from typing import Dict, List, Tuple, Optional
import random

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.model import create_model
from src.data_loader import IceDataset
from src.utils import load_checkpoint

plt.rcParams['font.family'] = 'SimHei'  # 设置中文字体


class SimpleIceVisualizer:
    """简单的道路结冰预测可视化器"""
    
    def __init__(self, config_path: str, model_path: str):
        """
        初始化可视化器
        
        Args:
            config_path: 配置文件路径
            model_path: 模型检查点路径
        """
        # 加载配置
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = json.load(f)
        
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 加载模型
        self.model = create_model(self.config['model']).to(self.device)
        checkpoint = load_checkpoint(model_path)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.model.eval()
        
        # 获取数据集划分信息
        self.train_events, self.test_events = self._get_event_splits()
        
        print(f"模型加载完成，设备: {self.device}")
        print(f"训练集事件数: {len(self.train_events)}")
        print(f"测试集事件数: {len(self.test_events)}")
    
    def _get_event_splits(self) -> Tuple[List[str], List[str]]:
        """获取训练集和测试集的事件划分"""
        # 创建临时数据集来获取事件划分信息
        train_dataset = IceDataset(
            data_dir=self.config['data']['data_dir'],
            sequence_length=self.config['data']['sequence_length'],
            prediction_horizon=self.config['data']['prediction_horizon'],
            train_ratio=self.config['data'].get('train_ratio', 0.8),
            mode='train',
            sample_ratio=1.0,  # 使用全部数据来获取完整的事件列表
            random_seed=self.config['data'].get('random_seed', 42)
        )
        
        test_dataset = IceDataset(
            data_dir=self.config['data']['data_dir'],
            sequence_length=self.config['data']['sequence_length'],
            prediction_horizon=self.config['data']['prediction_horizon'],
            train_ratio=self.config['data'].get('train_ratio', 0.8),
            mode='test',
            sample_ratio=1.0,
            random_seed=self.config['data'].get('random_seed', 42),
            shared_scalers=(train_dataset.scaler_weather, train_dataset.scaler_road)
        )
        
        return train_dataset.event_ids, test_dataset.event_ids
    
    def get_random_segments(self, num_segments: int = 4, random_seed: int = 42) -> List[Dict]:
        """
        从所有事件中随机选择指定数量的片段
        
        Args:
            num_segments: 要选择的片段数量
            random_seed: 随机种子
            
        Returns:
            包含片段信息的列表，每个元素包含event_file, start_idx等信息
        """
        random.seed(random_seed)
        np.random.seed(random_seed)
        
        # 获取所有CSV文件
        data_dir = self.config['data']['data_dir']
        csv_files = [f for f in os.listdir(data_dir) if f.endswith('.csv')]
        
        segments = []
        seq_len = self.config['data']['sequence_length']
        pred_len = self.config['data']['prediction_horizon']
        
        # 尝试从不同文件中选择片段
        attempts = 0
        max_attempts = len(csv_files) * 10  # 避免无限循环
        
        while len(segments) < num_segments and attempts < max_attempts:
            attempts += 1
            
            # 随机选择一个文件
            event_file = random.choice(csv_files)
            file_path = os.path.join(data_dir, event_file)
            
            try:
                df = pd.read_csv(file_path)
                df['datetime'] = pd.to_datetime(df['unix_time'], unit='s')
                df = df.sort_values('datetime').reset_index(drop=True)
                
                # 计算可预测的段数
                max_segments_in_file = len(df) - seq_len - pred_len + 1
                if max_segments_in_file <= 0:
                    continue
                
                # 随机选择一个起始位置
                start_idx = random.randint(0, max_segments_in_file - 1)
                
                segments.append({
                    'event_file': event_file,
                    'start_idx': start_idx,
                    'df': df
                })
                
            except Exception as e:
                print(f"处理文件 {event_file} 时出错: {e}")
                continue
        
        return segments[:num_segments]
    
    def predict_segment(self, event_file: str, df: pd.DataFrame, start_idx: int) -> Tuple[np.ndarray, np.ndarray, pd.DataFrame]:
        """
        对单个片段进行预测
        
        Args:
            event_file: 事件文件名
            df: 数据DataFrame
            start_idx: 起始索引
            
        Returns:
            predictions: 预测概率数组
            timestamps: 时间戳数组
            segment_df: 片段数据
        """
        # 确定事件属于训练集还是测试集
        event_id = event_file.replace('.csv', '')
        is_train = event_id in self.train_events
        
        # 创建对应的数据集来获取标准化器
        if is_train:
            dataset = IceDataset(
                data_dir=self.config['data']['data_dir'],
                sequence_length=self.config['data']['sequence_length'],
                prediction_horizon=self.config['data']['prediction_horizon'],
                train_ratio=self.config['data'].get('train_ratio', 0.8),
                mode='train',
                sample_ratio=1.0,
                random_seed=self.config['data'].get('random_seed', 42)
            )
        else:
            # 先创建训练集来获取标准化器
            train_dataset = IceDataset(
                data_dir=self.config['data']['data_dir'],
                sequence_length=self.config['data']['sequence_length'],
                prediction_horizon=self.config['data']['prediction_horizon'],
                train_ratio=self.config['data'].get('train_ratio', 0.8),
                mode='train',
                sample_ratio=1.0,
                random_seed=self.config['data'].get('random_seed', 42)
            )
            
            dataset = IceDataset(
                data_dir=self.config['data']['data_dir'],
                sequence_length=self.config['data']['sequence_length'],
                prediction_horizon=self.config['data']['prediction_horizon'],
                train_ratio=self.config['data'].get('train_ratio', 0.8),
                mode='test',
                sample_ratio=1.0,
                random_seed=self.config['data'].get('random_seed', 42),
                shared_scalers=(train_dataset.scaler_weather, train_dataset.scaler_road)
            )
        
        # 提取特征
        weather_features = [
            'weather_air_temp', 'weather_hum', 'weather_pressure',
            'weather_wind_speed', 'weather_wind_direction',
            'weather_rainfall', 'weather_rainfall_intensity'
        ]
        road_features = [
            'road_road_surface_temp', 'road_water_film_height',
            'road_freezing_temp', 'road_saltness'
        ]
        
        weather_data = df[weather_features].values
        road_data = df[road_features].values
        
        # 标准化数据
        weather_data = dataset.scaler_weather.transform(weather_data)
        road_data = dataset.scaler_road.transform(road_data)
        
        seq_len = self.config['data']['sequence_length']
        pred_len = self.config['data']['prediction_horizon']
        
        # 准备输入数据
        weather_history = weather_data[start_idx:start_idx + seq_len]
        weather_future = weather_data[start_idx + seq_len:start_idx + seq_len + pred_len]
        road_history = road_data[start_idx:start_idx + seq_len]
        
        # 转换为tensor并添加batch维度
        weather_history = torch.FloatTensor(weather_history).unsqueeze(0).to(self.device)
        weather_future = torch.FloatTensor(weather_future).unsqueeze(0).to(self.device)
        road_history = torch.FloatTensor(road_history).unsqueeze(0).to(self.device)
        
        # 预测
        with torch.no_grad():
            predictions = self.model(weather_history, weather_future, road_history, pred_len)
            predictions = torch.sigmoid(predictions).cpu().numpy()[0]  # 应用sigmoid并移除batch维度
        
        # 生成时间戳
        pred_start_time = df.iloc[start_idx + seq_len]['datetime']
        timestamps = [pred_start_time + timedelta(seconds=10*i) for i in range(pred_len)]
        
        # 获取片段数据（包含历史和预测部分）
        segment_df = df.iloc[start_idx:start_idx + seq_len + pred_len].copy()
        
        return predictions, np.array(timestamps), segment_df
    
    def create_simple_visualization(self, save_path: str = 'simple_prediction_visualization.png', 
                                  random_seed: int = 42):
        """
        创建简单的4子图可视化
        
        Args:
            save_path: 保存路径
            random_seed: 随机种子
        """
        # 获取4个随机片段
        segments = self.get_random_segments(num_segments=4, random_seed=random_seed)
        
        if len(segments) < 4:
            print(f"警告：只找到 {len(segments)} 个有效片段，少于期望的4个")
        
        # 创建2x2子图
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('道路结冰预测概率可视化 - 随机片段', fontsize=16)
        
        axes = axes.flatten()  # 将2x2数组展平为1D数组
        
        for i, segment in enumerate(segments):
            if i >= 4:  # 只处理前4个
                break
                
            ax = axes[i]
            
            try:
                # 获取预测结果
                predictions, timestamps, segment_df = self.predict_segment(
                    segment['event_file'], segment['df'], segment['start_idx']
                )
                
                # 确定数据集类型
                event_id = segment['event_file'].replace('.csv', '')
                dataset_type = "训练集" if event_id in self.train_events else "测试集"
                
                # 绘制实际凝冰背景（蓝色）
                ax.fill_between(segment_df['datetime'], 0, 1, 
                               where=segment_df['is_icing']==1, alpha=0.3, color='blue', 
                               label='实际凝冰时段')
                
                # 绘制预测概率曲线
                ax.plot(timestamps, predictions, 'r-', linewidth=2, label='预测概率')
                
                # 设置标题和标签
                ax.set_title(f'{event_id} ({dataset_type})', fontsize=12)
                ax.set_ylabel('概率', fontsize=10)
                ax.set_ylim(0, 1)
                ax.grid(True, alpha=0.3)
                ax.legend(fontsize=8)
                
                # 格式化时间轴
                ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
                ax.xaxis.set_major_locator(mdates.HourLocator(interval=2))
                plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
                
            except Exception as e:
                print(f"处理片段 {i+1} 时出错: {e}")
                ax.text(0.5, 0.5, f'处理出错\n{segment["event_file"]}', 
                       ha='center', va='center', transform=ax.transAxes)
                ax.set_title(f'片段 {i+1} - 错误', fontsize=12)
        
        # 隐藏多余的子图
        for i in range(len(segments), 4):
            axes[i].set_visible(False)
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"简单可视化已保存到: {save_path}")


def main():
    parser = argparse.ArgumentParser(description='简单的道路结冰预测可视化')
    parser.add_argument('--config', type=str, default='config.json', help='配置文件路径')
    parser.add_argument('--model', type=str, required=True, help='模型检查点路径')
    parser.add_argument('--output', type=str, default='simple_prediction_visualization.png', help='输出文件路径')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    
    args = parser.parse_args()
    
    # 检查文件是否存在
    if not os.path.exists(args.config):
        print(f"错误: 配置文件 {args.config} 不存在")
        return
    
    if not os.path.exists(args.model):
        print(f"错误: 模型文件 {args.model} 不存在")
        return
    
    # 创建可视化器
    visualizer = SimpleIceVisualizer(args.config, args.model)
    
    # 创建可视化
    visualizer.create_simple_visualization(args.output, args.seed)


if __name__ == "__main__":
    main()
