"""
数据加载和预处理模块
"""
import os
import pandas as pd
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler
from typing import List, Tuple, Dict, Optional
import warnings
warnings.filterwarnings('ignore')


class IceDataset(Dataset):
    """道路结冰预测数据集"""

    def __init__(self,
                 data_dir: str,
                 sequence_length: int = 60,
                 prediction_horizon: int = 12,
                 train_ratio: float = 0.8,
                 mode: str = 'train',
                 normalize: bool = True,
                 sample_ratio: float = 1.0,
                 random_seed: int = 42,
                 shared_scalers: tuple = None):
        """
        初始化数据集

        Args:
            data_dir: 数据目录路径
            sequence_length: 输入序列长度（历史时间步数）
            prediction_horizon: 预测时间范围（未来时间步数）
            train_ratio: 训练集比例（剩余为测试集）
            mode: 数据集模式 ('train', 'test')
            normalize: 是否标准化数据
            sample_ratio: 从每个事件中抽样的比例（0-1）
            random_seed: 随机种子，保证数据一致性
            shared_scalers: 共享的标准化器 (weather_scaler, road_scaler)
        """
        self.data_dir = data_dir
        self.sequence_length = sequence_length
        self.prediction_horizon = prediction_horizon
        self.mode = mode
        self.normalize = normalize
        self.sample_ratio = sample_ratio
        self.random_seed = random_seed

        # 设置随机种子
        np.random.seed(random_seed)

        # 定义特征列
        self.weather_features = [
            'weather_air_temp', 'weather_hum', 'weather_pressure',
            'weather_wind_speed', 'weather_wind_direction',
            'weather_rainfall', 'weather_rainfall_intensity'
        ]

        self.road_features = [
            'road_road_surface_temp', 'road_water_film_height',
            'road_freezing_temp', 'road_saltness'
        ]

        self.target_feature = 'is_icing'

        # 加载和预处理数据
        self.all_events_data = self._load_and_preprocess_data()

        # 按事件划分数据集
        self.event_ids = self._split_events_by_mode(train_ratio)

        # 创建或使用共享的标准化器
        if shared_scalers is not None:
            self.scaler_weather, self.scaler_road = shared_scalers
        else:
            self.scaler_weather = StandardScaler() if normalize else None
            self.scaler_road = StandardScaler() if normalize else None

        # 创建序列数据
        self.sequences = self._create_sequences()
        
    def _load_and_preprocess_data(self) -> Dict[str, pd.DataFrame]:
        """加载并预处理所有CSV文件，按事件组织"""
        all_events_data = {}

        # 获取所有CSV文件
        csv_files = [f for f in os.listdir(self.data_dir) if f.endswith('.csv')]
        csv_files.sort()  # 确保文件顺序一致

        for file in csv_files:
            file_path = os.path.join(self.data_dir, file)
            df = pd.read_csv(file_path)

            # 添加文件标识符
            event_id = file.replace('.csv', '')
            df['event_id'] = event_id

            # 转换时间戳
            df['datetime'] = pd.to_datetime(df['unix_time'], unit='s')

            # 转换布尔值
            df[self.target_feature] = df[self.target_feature].astype(int)

            # 按时间排序
            df = df.sort_values('datetime').reset_index(drop=True)

            # 只保留足够长的事件
            if len(df) >= self.sequence_length + self.prediction_horizon:
                all_events_data[event_id] = df

        return all_events_data

    def _split_events_by_mode(self, train_ratio: float) -> List[str]:
        """按事件长度更均匀地划分训练、测试集"""
        all_event_ids = list(self.all_events_data.keys())
        all_event_ids.sort()  # 确保顺序一致

        # 计算每个事件的长度和可生成的序列数
        event_info = []
        for event_id in all_event_ids:
            event_length = len(self.all_events_data[event_id])
            max_sequences = max(0, event_length - self.sequence_length - self.prediction_horizon + 1)
            event_info.append({
                'event_id': event_id,
                'length': event_length,
                'max_sequences': max_sequences
            })

        # 按长度排序，然后使用轮询方式分配，确保各集合长度分布均匀
        event_info.sort(key=lambda x: x['length'], reverse=True)

        # 设置随机种子并打乱（保持相同长度事件的随机性）
        np.random.seed(self.random_seed)
        np.random.shuffle(event_info)

        # 计算目标序列数
        total_sequences = sum(info['max_sequences'] for info in event_info)
        target_train_sequences = int(total_sequences * train_ratio)

        # 贪心分配：优先满足训练集，剩余为测试集
        train_events = []
        test_events = []

        train_sequences = 0

        for info in event_info:
            if train_sequences < target_train_sequences:
                train_events.append(info['event_id'])
                train_sequences += info['max_sequences']
            else:
                test_events.append(info['event_id'])

        # 按模式返回对应的事件ID
        if self.mode == 'train':
            return train_events
        else:  # test
            return test_events
    
    def _create_sequences(self) -> List[Dict]:
        """创建序列数据"""
        sequences = []

        # 收集所有训练数据用于拟合标准化器（仅在训练模式且标准化器未拟合时）
        if self.normalize and self.mode == 'train' and not hasattr(self.scaler_weather, 'mean_'):
            all_weather_data = []
            all_road_data = []

            for event_id in self.event_ids:
                event_data = self.all_events_data[event_id]
                all_weather_data.append(event_data[self.weather_features].values)
                all_road_data.append(event_data[self.road_features].values)

            # 拟合标准化器
            combined_weather = np.vstack(all_weather_data)
            combined_road = np.vstack(all_road_data)
            self.scaler_weather.fit(combined_weather)
            self.scaler_road.fit(combined_road)

        # 计算所有事件的长度信息，用于长度均衡采样
        event_lengths = {}
        total_max_sequences = 0
        for event_id in self.event_ids:
            event_data = self.all_events_data[event_id]
            max_sequences = len(event_data) - self.sequence_length - self.prediction_horizon + 1
            if max_sequences > 0:
                event_lengths[event_id] = max_sequences
                total_max_sequences += max_sequences

        # 计算目标总序列数
        target_total_sequences = int(total_max_sequences * self.sample_ratio)

        # 按事件处理
        for event_id in self.event_ids:
            event_data = self.all_events_data[event_id]

            # 提取特征
            weather_data = event_data[self.weather_features].values
            road_data = event_data[self.road_features].values
            target_data = event_data[self.target_feature].values

            # 标准化
            if self.normalize:
                weather_data = self.scaler_weather.transform(weather_data)
                road_data = self.scaler_road.transform(road_data)

            # 计算可能的序列数量
            max_sequences = len(event_data) - self.sequence_length - self.prediction_horizon + 1
            if max_sequences <= 0:
                continue

            # 根据事件长度均衡采样：长事件采样比例相对较低，短事件采样比例相对较高
            if total_max_sequences > 0:
                # 计算该事件的权重（长度越长，权重越小）
                length_weight = 1.0 / np.sqrt(max_sequences)  # 使用平方根来平衡长短事件

                # 计算所有事件的权重总和
                total_weight = sum(1.0 / np.sqrt(event_lengths[eid]) for eid in event_lengths)

                # 根据权重分配序列数
                event_target_sequences = int(target_total_sequences * length_weight / total_weight)

                # 确保至少有1个序列，但不超过最大可能序列数
                num_sequences = max(1, min(event_target_sequences, max_sequences))
            else:
                num_sequences = max(1, int(max_sequences * self.sample_ratio))

            # 随机选择起始位置
            np.random.seed(self.random_seed + hash(event_id) % 10000)  # 为每个事件设置不同但固定的种子
            start_indices = np.random.choice(max_sequences, size=num_sequences, replace=False)
            start_indices.sort()  # 保持时间顺序

            # 创建序列
            for start_idx in start_indices:
                # 历史天气数据（encoder输入）
                weather_seq = weather_data[start_idx:start_idx + self.sequence_length]

                # 未来天气数据（encoder输入，包含未来信息）
                future_weather_seq = weather_data[start_idx + self.sequence_length:start_idx + self.sequence_length + self.prediction_horizon]

                # 历史道路数据（decoder输入）
                road_seq = road_data[start_idx:start_idx + self.sequence_length]

                # 目标序列（未来is_icing）
                target_seq = target_data[start_idx + self.sequence_length:start_idx + self.sequence_length + self.prediction_horizon]

                sequences.append({
                    'weather_history': weather_seq,
                    'weather_future': future_weather_seq,
                    'road_history': road_seq,
                    'target': target_seq,
                    'event_id': event_id
                })

        return sequences
    
    def __len__(self) -> int:
        return len(self.sequences)
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        sequence = self.sequences[idx]
        
        return {
            'weather_history': torch.FloatTensor(sequence['weather_history']),
            'weather_future': torch.FloatTensor(sequence['weather_future']),
            'road_history': torch.FloatTensor(sequence['road_history']),
            'target': torch.FloatTensor(sequence['target']),
            'event_id': sequence['event_id']
        }


def create_data_loaders(data_dir: str,
                       batch_size: int = 32,
                       sequence_length: int = 60,
                       prediction_horizon: int = 12,
                       num_workers: int = 4,
                       sample_ratio: float = 1.0,
                       random_seed: int = 42,
                       train_ratio: float = 0.8) -> Tuple[DataLoader, DataLoader]:
    """
    创建训练和测试数据加载器

    Args:
        data_dir: 数据目录路径
        batch_size: 批次大小
        sequence_length: 输入序列长度
        prediction_horizon: 预测时间范围
        num_workers: 数据加载工作进程数
        sample_ratio: 从每个事件中抽样的比例（0-1）
        random_seed: 随机种子，保证数据一致性
        train_ratio: 训练集比例

    Returns:
        train_loader, test_loader
    """

    # 首先创建训练数据集来拟合标准化器
    train_dataset = IceDataset(
        data_dir=data_dir,
        sequence_length=sequence_length,
        prediction_horizon=prediction_horizon,
        train_ratio=train_ratio,
        mode='train',
        sample_ratio=sample_ratio,
        random_seed=random_seed
    )

    # 获取拟合好的标准化器
    fitted_scaler_weather = train_dataset.scaler_weather
    fitted_scaler_road = train_dataset.scaler_road

    # 创建测试数据集，传入共享的标准化器
    test_dataset = IceDataset(
        data_dir=data_dir,
        sequence_length=sequence_length,
        prediction_horizon=prediction_horizon,
        train_ratio=train_ratio,
        mode='test',
        sample_ratio=sample_ratio,
        random_seed=random_seed,
        shared_scalers=(fitted_scaler_weather, fitted_scaler_road)
    )
    
    # 创建数据加载器
    # 设置随机种子以确保shuffle的一致性
    def seed_worker(worker_id):
        worker_seed = torch.initial_seed() % 2**32
        np.random.seed(worker_seed)

    generator = torch.Generator()
    generator.manual_seed(random_seed)

    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=True,
        worker_init_fn=seed_worker,
        generator=generator
    )

    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True,
        worker_init_fn=seed_worker
    )

    return train_loader, test_loader


if __name__ == "__main__":
    # 测试数据加载器
    data_dir = "../ice_events"
    train_loader, test_loader = create_data_loaders(
        data_dir,
        sample_ratio=0.1,  # 只使用10%的数据进行测试
        random_seed=42,
        train_ratio=0.8
    )

    print(f"训练集批次数: {len(train_loader)}")
    print(f"测试集批次数: {len(test_loader)}")

    # 查看一个批次的数据
    for batch in train_loader:
        print("批次数据形状:")
        for key, value in batch.items():
            if isinstance(value, torch.Tensor):
                print(f"  {key}: {value.shape}")
            else:
                print(f"  {key}: {type(value)}")
        break

    # 验证数据一致性
    print("\n验证数据一致性...")

    # 设置torch随机种子以确保DataLoader的一致性
    torch.manual_seed(42)
    train_loader2, test_loader2 = create_data_loaders(
        data_dir,
        sample_ratio=0.1,
        random_seed=42,  # 相同的随机种子
        train_ratio=0.8
    )

    # 重置随机种子，确保两个loader从相同状态开始
    torch.manual_seed(42)
    batch1 = next(iter(train_loader))

    torch.manual_seed(42)
    batch2 = next(iter(train_loader2))

    is_same = torch.allclose(batch1['weather_history'], batch2['weather_history'])
    print(f"数据一致性检查: {'通过' if is_same else '失败'}")

    # 验证事件划分信息
    print(f"\n事件划分信息:")
    print(f"训练事件数: {len(train_loader.dataset.event_ids)}")
    print(f"测试事件数: {len(test_loader.dataset.event_ids)}")

    # 验证事件不重叠
    train_events = set(train_loader.dataset.event_ids)
    test_events = set(test_loader.dataset.event_ids)
    all_events = train_events | test_events
    total_unique = len(all_events)
    total_assigned = len(train_events) + len(test_events)
    print(f"事件划分检查: {'通过' if total_unique == total_assigned else '失败'} (无重叠)")

    # 显示标准化器状态
    print(f"\n标准化器状态:")
    print(f"训练集标准化器已拟合: {hasattr(train_loader.dataset.scaler_weather, 'mean_')}")
    print(f"测试集标准化器已拟合: {hasattr(test_loader.dataset.scaler_weather, 'mean_')}")
    print(f"测试集与训练集共享标准化器: {test_loader.dataset.scaler_weather is train_loader.dataset.scaler_weather}")
